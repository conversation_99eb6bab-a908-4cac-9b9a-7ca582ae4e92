import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Chemical from '@/models/Chemical';
import Regulation from '@/models/Regulation';
import ChemicalRegulationV2 from '@/models/ChemicalRegulationV2';
import * as XLSX from 'xlsx';

// For debugging
console.log('Import API: Models loaded', {
  Chemical: !!Chemical,
  Regulation: !!Regulation,
  ChemicalRegulationV2: !!ChemicalRegulationV2
});

// Add runtime directive for Node.js
export const runtime = 'nodejs';

// Maximum file size (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

interface RowData {
  [key: string]: any;
}

export async function POST(request: NextRequest) {
  try {
    // Check if the request is multipart/form-data
    const contentType = request.headers.get('content-type') || '';
    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json(
        { success: false, error: 'Request must be multipart/form-data' },
        { status: 400 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Parse the form data
    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const regulationNameRaw = formData.get('regulationName');
    const regulationName = regulationNameRaw ? String(regulationNameRaw) : '';
    const regulationIdRaw = formData.get('regulationId');
    const regulationId = regulationIdRaw ? String(regulationIdRaw) : '';
    const categoryRaw = formData.get('category');
    const category = categoryRaw ? String(categoryRaw) : 'Imported';
    const regionRaw = formData.get('region');
    const region = regionRaw ? String(regionRaw) : 'Global';

    console.log('Import request parameters:', {
      regulationName,
      regulationId,
      category,
      region,
      fileName: file?.name
    });

    // Check if a file was provided
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { success: false, error: `File size exceeds the limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB` },
        { status: 400 }
      );
    }

    // Get file details
    const fileName = file.name;
    const fileType = file.type;

    // Check file type
    if (!fileType.includes('spreadsheet') && !fileType.includes('csv') &&
        !fileName.endsWith('.xlsx') && !fileName.endsWith('.xls') && !fileName.endsWith('.csv')) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only Excel and CSV files are supported.' },
        { status: 400 }
      );
    }

    // Convert the file to an array buffer
    const fileBuffer = await file.arrayBuffer();

    // Parse the Excel/CSV file
    const workbook = XLSX.read(fileBuffer, { type: 'array' });
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    const data: RowData[] = XLSX.utils.sheet_to_json(worksheet);

    console.log(`Parsed Excel file: ${data.length} rows found`);
    if (data.length > 0) {
      console.log('Available columns:', Object.keys(data[0]));
      console.log('First row sample:', data[0]);
    }

    if (data.length === 0) {
      return NextResponse.json(
        { success: false, error: 'File contains no data' },
        { status: 400 }
      );
    }

    // Process the data
    const results = {
      added: 0,
      updated: 0,
      skipped: 0,
      errors: [] as string[],
      chemicals: [] as any[]
    };

    // Create or find regulation if regulationName or regulationId is provided
    let regulation = null;

    if (regulationId) {
      console.log(`Looking for regulation with ID: ${regulationId}`);
      regulation = await Regulation.findById(regulationId);
      if (!regulation) {
        return NextResponse.json(
          { success: false, error: 'Regulation not found' },
          { status: 404 }
        );
      }
      console.log(`Found regulation: ${regulation.name}`);
    } else if (regulationName && regulationName.trim() !== '') {
      console.log(`Looking for regulation with name: ${regulationName}`);
      // Try to find an existing regulation with this name
      regulation = await Regulation.findOne({
        name: { $regex: new RegExp('^' + regulationName.trim() + '$', 'i') }
      });

      if (regulation) {
        console.log(`Found existing regulation: ${regulation.name}`);
      } else {
        // Create a new regulation with the provided name
        console.log(`Creating new regulation: ${regulationName}`);
        regulation = await Regulation.create({
          name: regulationName.trim(),
          country: 'Unknown',
          region: region,
          description: `Imported from ${fileName}`,
          categories: [category],
          lastUpdated: new Date()
        });
        console.log(`Created new regulation with ID: ${regulation._id}`);
      }
    } else {
      console.log('No regulation ID or name provided');
    }

    // Process each row in the file
    for (const row of data) {
      try {        // Find chemical name column (could be "chemical name", "substance name", etc.)
        const chemicalNameKeys = Object.keys(row).filter(key =>
          (key.toLowerCase().includes('chemical') && key.toLowerCase().includes('name')) ||
          (key.toLowerCase().includes('substance') && key.toLowerCase().includes('name')) ||
          key.toLowerCase() === 'name' ||
          key.toLowerCase() === 'substance name' ||
          key.toLowerCase() === 'chemical name' ||
          key.toLowerCase() === 'substance' ||
          key.toLowerCase().includes('compound')
        );        // Find CAS number column
        const casNumberKeys = Object.keys(row).filter(key =>
          key.toLowerCase().includes('cas') ||
          key.toLowerCase().includes('cas no') ||
          key.toLowerCase().includes('cas number') ||
          key.toLowerCase() === 'cas no.' ||
          key.toLowerCase() === 'cas'
        );

        // Find EC number column
        const ecNumberKeys = Object.keys(row).filter(key =>
          (key.toLowerCase().includes('ec') && key.toLowerCase().includes('number')) ||
          key.toLowerCase().includes('ec no') ||
          key.toLowerCase().includes('ec-number') ||
          key.toLowerCase().includes('einecs') ||
          key.toLowerCase() === 'ec'
        );

        // Find SML value column
        const smlKeys = Object.keys(row).filter(key =>
          key.toLowerCase().includes('sml') ||
          key.toLowerCase().includes('specific migration') ||
          key.toLowerCase().includes('migration limit')
        );

        // Find notes column
        const notesKeys = Object.keys(row).filter(key =>
          key.toLowerCase().includes('note') ||
          key.toLowerCase().includes('comment') ||
          key.toLowerCase().includes('remark')
        );

        // Find restrictions column
        const restrictionsKeys = Object.keys(row).filter(key =>
          key.toLowerCase().includes('restriction') ||
          key.toLowerCase().includes('limitation') ||
          key.toLowerCase().includes('condition')
        );        // Helper function to normalize empty values
        const normalizeValue = (value: any): string => {
          if (!value) return '';
          const str = String(value).trim();
          // Treat these as empty values
          if (str === '-' || str === 'N/A' || str === 'n/a' || str === 'Not specified' || str === 'not specified' || str === '') {
            return '';
          }
          return str;
        };

        // Extract values and normalize them
        const chemicalName = chemicalNameKeys.length > 0 ? normalizeValue(row[chemicalNameKeys[0]]) : '';
        const casNumber = casNumberKeys.length > 0 ? normalizeValue(row[casNumberKeys[0]]) : '';
        const ecNumber = ecNumberKeys.length > 0 ? normalizeValue(row[ecNumberKeys[0]]) : '';
        let smlValue = smlKeys.length > 0 ? normalizeValue(row[smlKeys[0]]) : '';
        const notes = notesKeys.length > 0 ? normalizeValue(row[notesKeys[0]]) : '';
        const restrictions = restrictionsKeys.length > 0 ? normalizeValue(row[restrictionsKeys[0]]) : '';

        // Extract SML value and unit
        let smlUnit = 'mg/kg'; // Default unit
        if (smlValue && typeof smlValue === 'string') {
          // Extract numeric part from SML value (e.g., "0.05 mg/kg" -> "0.05")
          const numericMatch = smlValue.match(/[\d.]+/);
          if (numericMatch) {
            // Try to extract the unit part
            const unitMatch = smlValue.match(/[a-zA-Z\/]+/g);
            if (unitMatch && unitMatch.length > 0) {
              smlUnit = unitMatch.join('').trim();
            }
            smlValue = numericMatch[0];
          }
        }

        // Collect additional information from other columns
        const additionalInfo: Record<string, any> = {};
        Object.keys(row).forEach(key => {
          // Skip columns we've already processed
          if (!chemicalNameKeys.includes(key) &&
              !casNumberKeys.includes(key) &&
              !ecNumberKeys.includes(key) &&
              !smlKeys.includes(key) &&
              !notesKeys.includes(key) &&
              !restrictionsKeys.includes(key)) {
            additionalInfo[key] = row[key];
          }
        });        // Skip row only if BOTH chemical name AND CAS number are missing (after normalization)
        // We should import rows that have either a chemical name OR a CAS number
        if (!chemicalName && !casNumber) {
          console.log('Skipping row - no valid chemical name or CAS number:', {
            originalName: chemicalNameKeys.length > 0 ? row[chemicalNameKeys[0]] : 'N/A',
            originalCAS: casNumberKeys.length > 0 ? row[casNumberKeys[0]] : 'N/A',
            availableColumns: Object.keys(row),
            rowData: row
          });
          results.skipped++;
          continue;
        }

        // Log what we found for debugging
        console.log('Processing row with data:', {
          chemicalName: chemicalName || 'MISSING',
          casNumber: casNumber || 'MISSING',
          chemicalNameColumn: chemicalNameKeys.length > 0 ? chemicalNameKeys[0] : 'NOT_FOUND',
          casNumberColumn: casNumberKeys.length > 0 ? casNumberKeys[0] : 'NOT_FOUND',
          originalChemicalName: chemicalNameKeys.length > 0 ? row[chemicalNameKeys[0]] : 'N/A',
          originalCasNumber: casNumberKeys.length > 0 ? row[casNumberKeys[0]] : 'N/A'
        });

// For chemicals without CAS numbers, we need to generate a unique identifier
        // Use a counter to ensure uniqueness even with rapid processing
        let finalCasNumber = casNumber;
        let finalChemicalName = chemicalName;

        if (!casNumber) {
          // Generate a more robust unique identifier
          const timestamp = Date.now();
          const randomId = Math.random().toString(36).substr(2, 9);
          const counter = results.added + results.updated + results.skipped + 1;
          finalCasNumber = `UNKNOWN-${timestamp}-${counter}-${randomId}`;
        }

        if (!chemicalName) {
          finalChemicalName = `Unknown Chemical (${finalCasNumber})`;
        }// Try to find existing chemical by CAS number first, then by name
        let existingChemical = null;
        if (casNumber) {
          existingChemical = await Chemical.findOne({ casNumber: casNumber });
        }

        if (!existingChemical && chemicalName) {
          existingChemical = await Chemical.findOne({ name: chemicalName });
        }

        if (existingChemical) {
          // Chemical exists, just update the count
          results.updated++;
          results.chemicals.push({
            _id: existingChemical._id,
            name: existingChemical.name,
            casNumber: existingChemical.casNumber,
            updated: true
          });

          console.log(`Found existing chemical: ${existingChemical.name}`);        } else {
          // Validate that we have the required fields before creating
          if (!finalChemicalName || !finalCasNumber) {
            console.error('Missing required fields for chemical creation:', {
              finalChemicalName,
              finalCasNumber,
              originalChemicalName: chemicalName,
              originalCasNumber: casNumber,
              rowData: row
            });
            results.errors.push(`Missing required fields - Name: "${finalChemicalName}", CAS: "${finalCasNumber}" (Original: Name="${chemicalName}", CAS="${casNumber}")`);
            results.skipped++;
            continue;
          }

          // Create new chemical using the correct schema with finalized values
          const newChemical = await Chemical.create({
            name: finalChemicalName,
            casNumber: finalCasNumber,
            status: 'unknown',
            riskLevel: 'unknown',
            riskDescription: notes || undefined
          });

          results.added++;
          results.chemicals.push({
            _id: newChemical._id,
            name: newChemical.name,
            casNumber: newChemical.casNumber,
            added: true
          });

          console.log(`Created new chemical: ${newChemical.name} (CAS: ${newChemical.casNumber})`);
          existingChemical = newChemical;
        }

        // If regulation is provided, create a chemical-regulation relationship
        if (regulation && existingChemical) {
          try {
            console.log(`Creating chemical-regulation relationship for ${existingChemical.name} -> ${regulation.name}`);

            // Check if relationship already exists
            const existingRelation = await ChemicalRegulationV2.findOne({
              chemical: existingChemical._id,
              regulation: regulation._id
            });

            if (!existingRelation) {
              // Create new chemical-regulation relationship
              await ChemicalRegulationV2.create({
                chemical: existingChemical._id,
                regulation: regulation._id,
                smlValue: smlValue || '',
                smlUnit: smlUnit,
                notes: notes,
                restrictions: restrictions,
                additionalInfo: additionalInfo
              });

              console.log(`Created chemical-regulation relationship`);
            } else {
              // Update existing relationship if new data is provided
              let updated = false;

              if (smlValue && existingRelation.smlValue !== smlValue) {
                existingRelation.smlValue = smlValue;
                existingRelation.smlUnit = smlUnit;
                updated = true;
              }

              if (notes && existingRelation.notes !== notes) {
                existingRelation.notes = notes;
                updated = true;
              }

              if (restrictions && existingRelation.restrictions !== restrictions) {
                existingRelation.restrictions = restrictions;
                updated = true;
              }

              if (Object.keys(additionalInfo).length > 0) {
                existingRelation.additionalInfo = { ...existingRelation.additionalInfo, ...additionalInfo };
                updated = true;
              }

              if (updated) {
                await existingRelation.save();
                console.log(`Updated existing chemical-regulation relationship`);
              }
            }
          } catch (relationError) {
            console.error(`Error creating chemical-regulation relationship:`, relationError);
            const errorMessage = relationError instanceof Error ? relationError.message : 'Unknown error';
            results.errors.push(`Error linking chemical ${existingChemical.name} to regulation ${regulation.name}: ${errorMessage}`);
          }
        }
      } catch (rowError) {
        console.error('Error processing row:', rowError, row);
        const errorMessage = rowError instanceof Error ? rowError.message : 'Unknown error';
        results.errors.push(`Error processing row: ${errorMessage}`);
        results.skipped++;
      }
    }

    // Log final summary
    console.log('Import completed:', {
      totalRows: data.length,
      added: results.added,
      updated: results.updated,
      skipped: results.skipped,
      errors: results.errors.length,
      regulation: regulation?.name || 'None'
    });

    // Prepare the response
    const response = {
      success: true,
      data: {
        ...results,
        regulation: regulation ? {
          _id: regulation._id,
          name: regulation.name
        } : null,
        totalRows: data.length
      },
      message: `Processed ${data.length} rows: ${results.added} added, ${results.updated} updated, ${results.skipped} skipped`
    };

    if (results.errors.length > 0) {
      response.message += ` (${results.errors.length} errors)`;
    }

    if (regulation) {
      response.message += ` (Regulation: ${regulation.name})`;
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error importing chemicals:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}
